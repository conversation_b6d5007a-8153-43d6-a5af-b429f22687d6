@layer legacy {
    .radio {
        --apl-radio-width: 20px;
        --apl-radio-height: 20px;
        --apl-radio-border-color: var(--apl-colors-surface-static-ui-active);
        --apl-radio-hover-border-color: var(--apl-colors-border-primary-subdued);
        --apl-radio-focus-border-color: var(--apl-colors-surface-action-primary-default);
        --apl-radio-disabled-border-color: var(--apl-colors-surface-action-primary-default);
        --apl-radio-border: 2px solid var(--apl-radio-border-color);
        --apl-radio-background-color: var(--apl-colors-surface-static-ui-default);
        --apl-radio-hover-background-color: var(--apl-colors-surface-static-ui-default);
        --apl-radio-focus-background-color: var(--apl-colors-surface-action-primary-default);
        --apl-radio-disabled-background-color: var(--apl-colors-surface-static-ui-disabled);
        --apl-radio-outline: 3px solid var(--apl-colors-border-focus);
        /* indicator */
        --apl-radio-indicator-background-color: var(--apl-colors-surface-static-ui-default);
        --apl-radio-indicator-disabled-background-color: var(--apl-colors-border-disabled);
        /* checked */
        --apl-radio-checked-border-color: var(--apl-colors-surface-action-primary-default);
        --apl-radio-checked-hover-border-color: var(--apl-colors-border-primary-subdued);
        --apl-radio-checked-background-color: var(--apl-colors-surface-action-primary-default);
        --apl-radio-checked-hover-background-color: var(--apl-colors-surface-action-primary-hover);
        /* radioLabel */
        --apl-radio-label-gap: var(--apl-space-gap-xs);
        /* radioGroup */
        --apl-radio-group-gap: var(--apl-space-gap-sm);
        --apl-radio-group-disabled-color: var(--apl-colors-content-disabled);

    }
}

@layer apollo {
    .radio {
        --apl-radio-width: 22px;
        --apl-radio-height: 22px;
        --apl-radio-border-color: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
        --apl-radio-hover-border-color: var(--apl-alias-color-primary-hovered, #2C8745);
        --apl-radio-focus-border-color: var(--apl-alias-color-primary-focused, #49A25C);
        --apl-radio-disabled-border-color: var(--apl-alias-color-outline-and-border-outline, #F8F7F7);
        --apl-radio-border: 1px solid var(--apl-radio-border-color);
        --apl-radio-background-color: var(--apl-alias-color-background-and-surface-background, #FFFFFF);
        --apl-radio-hover-background-color: var(--apl-radio-background-color);
        --apl-radio-focus-background-color: var(--apl-radio-background-color);
        --apl-radio-disabled-background-color: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
        --apl-radio-outline: 2px solid var(--apl-alias-color-primary-primary-container, #C5FFC8);
        /* indicator */
        --apl-radio-indicator-background-color: var(--apl-alias-color-background-and-surface-surface-variant, #FFFFFF);
        --apl-radio-indicator-disabled-background-color: var(--apl-alias-color-background-and-surface-on-surface-variant, #787777);
        /* checked */
        --apl-radio-checked-border-color: var(--apl-alias-color-primary-primary, #016E2E);
        --apl-radio-checked-hover-border-color: var(--apl-alias-color-primary-hovered, #2C8745);
        --apl-radio-checked-focus-border-color: var(--apl-alias-color-primary-focused, #49A25C);
        --apl-radio-checked-background-color: var(--apl-alias-color-primary-primary, #016E2E);
        --apl-radio-checked-hover-background-color: var(--apl-alias-color-primary-hovered, #2C8745);
        --apl-radio-checked-focus-background-color: var(--apl-alias-color-primary-focused, #49A25C);
        /* radioLabel */
        --apl-radio-label-gap: var(--apl-alias-spacing-gap-gap5);
        --apl-radio-label-color: var(--apl-alias-color-background-and-surface-on-surface);
        /* radioGroup */
        --apl-radio-group-gap: 10px;
        --apl-radio-group-disabled-color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
    }
}

.radioRoot {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 22px;
    height: 22px;
    border: var(--apl-radio-border);
    border-color: var(--apl-radio-border-color);
    border-radius: 50%;
    background-color: var(--apl-radio-background-color);

    composes: apl-transition-all from '../../base.module.css';

    &:hover:not([data-disabled]) {
        --apl-radio-border-color: var(--apl-radio-hover-border-color);
        --apl-radio-background-color: var(--apl-radio-hover-background-color);
    }



    &:focus-visible,
    &:active {

        &:not([data-disabled]) {
            outline: var(--apl-radio-outline);
            outline-offset: 1px;
            --apl-radio-background-color: var(--apl-radio-focus-background-color);
            --apl-radio-border-color: var(--apl-radio-focus-border-color);
        }
    }

    &[data-checked]:not([data-disabled]) {
        --apl-radio-background-color: var(--apl-radio-checked-background-color);
        --apl-radio-border-color: var(--apl-radio-checked-border-color);

        &:hover {
            --apl-radio-background-color: var(--apl-radio-checked-hover-background-color);
            --apl-radio-border-color: var(--apl-radio-checked-hover-border-color);
        }

        &:focus-visible,
        &:active {
            --apl-radio-background-color: var(--apl-radio-checked-focus-background-color);
            --apl-radio-border-color: var(--apl-radio-checked-focus-border-color);
        }
    }

        &[data-disabled] {
        --apl-radio-background-color: var(--apl-radio-disabled-background-color);
        --apl-radio-border-color: var(--apl-radio-disabled-border-color);
    }

}


.indicator {
    width: 8px;
    height: 8px;
    aspect-ratio: 1;
    background-color: var(--apl-radio-indicator-background-color);
    border-radius: 50%;
    visibility: hidden;

    &[data-checked] {
        visibility: visible;
    }

    &[data-disabled] {
        --apl-radio-indicator-background-color: var(--apl-radio-indicator-disabled-background-color);
    }
}

.radioLabel {
    display: inline-flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: var(--apl-radio-label-gap);
    color: var(--apl-radio-label-color);
}

.radioGroup {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: var(--apl-radio-group-gap);

    &[data-disabled] {
        & :global(.ApolloRadio-labelText) {
            color: var(--apl-radio-group-disabled-color)
        }
    }

}

.radioGroupHorizontal {
    flex-direction: row;
}

.labelTextDisabled {
    color: var(--apl-radio-group-disabled-color);
}