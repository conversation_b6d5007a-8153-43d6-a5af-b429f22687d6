import { useCallback, useState } from "react"
import { ComponentRules, CSSClassesTable, UsageGuidelines } from "@/components"
import { Button, Radio, RadioGroup, Typography } from "@apollo/ui"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { <PERSON>a, StoryObj } from "@storybook/react"

/**
 * RadioGroup component
 *
 * The RadioGroup component provides a container for managing a group of radio buttons
 * with support for controlled and uncontrolled state, horizontal/vertical layouts,
 * and disabled functionality. Radio buttons within the group are mutually exclusive.
 *
 * Notes:
 * - Manages state for all Radio components within the group;
 * - Supports both controlled and uncontrolled usage patterns;
 * - Built on top of Base UI for accessibility.
 */
const meta = {
  title: "@apollo∕ui/Components/Inputs/Radio",
  component: RadioGroup,
  subcomponents: { Radio },
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/5ndGGLsLILgxao32M6rJ0F/Apollo-Design-System?node-id=520-7793&m=dev",
    },
    docs: {
      description: {
        component:
          "The RadioGroup component manages a group of radio buttons with Apollo design system styling. Supports controlled and uncontrolled state, horizontal/vertical layouts, and disabled functionality. Radio buttons within the group are mutually exclusive.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source code={`import { Radio, RadioGroup } from "@apollo/ui"`} language="tsx" />
          <h2 id="radiogroup-props">RadioGroup Props</h2>
          <ArgTypes />
          <h2 id="radio-props">Radio Props</h2>
          <div style={{ marginBottom: 24 }}>
            <p>The Radio component accepts the following props:</p>
            <table style={{ width: "100%", borderCollapse: "collapse", marginTop: 16 }}>
              <thead>
                <tr style={{ borderBottom: "2px solid #e5e7eb" }}>
                  <th style={{ textAlign: "left", padding: "12px 8px", fontWeight: 600 }}>Prop</th>
                  <th style={{ textAlign: "left", padding: "12px 8px", fontWeight: 600 }}>Type</th>
                  <th style={{ textAlign: "left", padding: "12px 8px", fontWeight: 600 }}>Default</th>
                  <th style={{ textAlign: "left", padding: "12px 8px", fontWeight: 600 }}>Description</th>
                </tr>
              </thead>
              <tbody>
                <tr style={{ borderBottom: "1px solid #e5e7eb" }}>
                  <td style={{ padding: "12px 8px", fontFamily: "monospace", fontSize: "14px" }}>children</td>
                  <td style={{ padding: "12px 8px", fontFamily: "monospace", fontSize: "14px" }}>ReactNode</td>
                  <td style={{ padding: "12px 8px" }}>-</td>
                  <td style={{ padding: "12px 8px" }}>The label content for the radio button. Displayed next to the radio button.</td>
                </tr>
                <tr style={{ borderBottom: "1px solid #e5e7eb" }}>
                  <td style={{ padding: "12px 8px", fontFamily: "monospace", fontSize: "14px" }}>value</td>
                  <td style={{ padding: "12px 8px", fontFamily: "monospace", fontSize: "14px" }}>string | number</td>
                  <td style={{ padding: "12px 8px" }}>-</td>
                  <td style={{ padding: "12px 8px" }}>The value of the radio button when selected.</td>
                </tr>
                <tr style={{ borderBottom: "1px solid #e5e7eb" }}>
                  <td style={{ padding: "12px 8px", fontFamily: "monospace", fontSize: "14px" }}>disabled</td>
                  <td style={{ padding: "12px 8px", fontFamily: "monospace", fontSize: "14px" }}>boolean</td>
                  <td style={{ padding: "12px 8px" }}>false</td>
                  <td style={{ padding: "12px 8px" }}>Disables the radio button.</td>
                </tr>
                <tr style={{ borderBottom: "1px solid #e5e7eb" }}>
                  <td style={{ padding: "12px 8px", fontFamily: "monospace", fontSize: "14px" }}>labelProps</td>
                  <td style={{ padding: "12px 8px", fontFamily: "monospace", fontSize: "14px" }}>ComponentProps&lt;'label'&gt;</td>
                  <td style={{ padding: "12px 8px" }}>-</td>
                  <td style={{ padding: "12px 8px" }}>Additional props to pass to the label element.</td>
                </tr>
                <tr style={{ borderBottom: "1px solid #e5e7eb" }}>
                  <td style={{ padding: "12px 8px", fontFamily: "monospace", fontSize: "14px" }}>className</td>
                  <td style={{ padding: "12px 8px", fontFamily: "monospace", fontSize: "14px" }}>string</td>
                  <td style={{ padding: "12px 8px" }}>-</td>
                  <td style={{ padding: "12px 8px" }}>Additional CSS class names to apply to the root element.</td>
                </tr>
              </tbody>
            </table>
          </div>
          <h2 id="radio-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use radio buttons to: Allow users to select one option from a set of mutually exclusive options",
              "Always use Radio components within a RadioGroup for proper functionality and accessibility",
              "Use clear, descriptive labels that explain what each option represents",
              "Keep option labels concise and scannable",
              "Group related radio buttons together with clear section headings",
              "Ensure sufficient spacing between radio buttons for easy interaction",
              "Use disabled state for options that are temporarily unavailable",
              "Provide a default selected option when appropriate",
              "Use vertical layout for better readability when you have multiple options",
            ]}
          />
          <h2 id="radio-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide descriptive labels through the <code>children</code> prop to ensure the
                radio button's purpose is clear to all users.
              </>,
              <>
                Use the <code>disabled</code> prop to disable radio buttons that are
                not currently actionable, ensuring they are not focusable.
              </>,
              "Ensure sufficient color contrast between radio button states and background in all conditions.",
              <>
                Use <code>RadioGroup</code> to group related radio buttons and provide
                proper keyboard navigation and ARIA attributes.
              </>,
              "Provide clear instructions about the selection behavior when needed.",
            ]}
          />
          <h2 id="radio-css-classes">CSS Classes</h2>
          <CSSClassesTable
            description="The Radio component provides several CSS classes that can be used for custom styling. These classes follow the Apollo design system naming conventions and provide access to different parts and states of the component."
            data={[
              {
                cssClassName: ".ApolloRadioGroup-root",
                description: "Styles applied to the radio group wrapper element",
                usageNotes: "Use for group layout and spacing customization",
              },
              {
                cssClassName: ".ApolloRadio-root",
                description: "Styles applied to the radio input element",
                usageNotes:
                  "Target for radio-specific styling (size, border, background)",
              },
               {
                cssClassName: ".ApolloRadio-label",
                description: "Styles applied to the label wrapper element",
                usageNotes: "Use for overall component positioning and spacing",
              },
              {
                cssClassName: ".ApolloRadio-indicator",
                description: "Styles applied to the radio indicator element",
                usageNotes: "Use for indicator styling and animation customization",
              },
              {
                cssClassName: ".ApolloRadio-labelText",
                description: "Styles applied to the label text element",
                usageNotes:
                  "Use for label text typography and spacing customization",
              },
            ]}
          />

          <h2 id="radio-examples">Examples</h2>
          <Stories title="" />
          <h2 id="radio-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 8,
                      }}
                    >
                      <Typography level="bodyLarge" style={{ fontWeight: "600", marginBottom: 8 }}>
                        Payment Method
                      </Typography>
                      <RadioGroup name="payment" defaultValue="credit">
                        <Radio value="credit">Credit Card</Radio>
                        <Radio value="debit">Debit Card</Radio>
                        <Radio value="paypal">PayPal</Radio>
                      </RadioGroup>
                    </div>
                  ),
                  description:
                    "Use clear, specific labels that describe the option being selected",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 8,
                      }}
                    >
                      <Typography level="bodyLarge" style={{ fontWeight: "600", marginBottom: 8 }}>
                        Options
                      </Typography>
                      <RadioGroup name="options" defaultValue="option1">
                        <Radio value="option1">Option 1</Radio>
                        <Radio value="option2">Option 2</Radio>
                        <Radio value="option3">Option 3</Radio>
                      </RadioGroup>
                    </div>
                  ),
                  description:
                    "Avoid generic labels that don't explain what the option represents",
                },
              },
              {
                positive: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 16,
                      }}
                    >
                      <RadioGroup name="size" defaultValue="medium">
                        <Radio value="small">Small</Radio>
                        <Radio value="medium">Medium</Radio>
                        <Radio value="large">Large</Radio>
                      </RadioGroup>
                    </div>
                  ),
                  description:
                    "Provide adequate spacing between radio buttons for easy interaction",
                },
                negative: {
                  component: (
                    <div
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 2,
                      }}
                    >
                      <RadioGroup name="size-bad" defaultValue="medium">
                        <Radio value="small">Small</Radio>
                        <Radio value="medium">Medium</Radio>
                        <Radio value="large">Large</Radio>
                      </RadioGroup>
                    </div>
                  ),
                  description:
                    "Don't place radio buttons too close together - it makes them hard to interact with",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    direction: {
      control: { type: "select" },
      options: ["horizontal", "vertical"],
      description: "The layout direction of the radio group.",
      table: {
        type: { summary: '"horizontal" | "vertical"' },
        defaultValue: { summary: '"vertical"' }
      },
    },
    value: {
      control: { type: "text" },
      description: "The currently selected value (controlled mode).",
      table: { type: { summary: "any" } },
    },
    defaultValue: {
      control: { type: "text" },
      description: "The default selected value (uncontrolled mode).",
      table: { type: { summary: "any" } },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Disables all radio buttons in the group.",
      table: { type: { summary: "boolean" } },
    },
    name: {
      control: { type: "text" },
      description: "The name attribute for the radio group.",
      table: { type: { summary: "string" } },
    },
    required: {
      control: { type: "boolean" },
      description: "Whether the radio group is required in a form.",
      table: { type: { summary: "boolean" } },
    },
    onValueChange: {
      control: false,
      description: "Callback fired when the selected value changes.",
      table: {
        type: {
          summary: "(value: any, event: Event) => void",
        },
      },
    },
    children: {
      control: false,
      description: "Radio components to be rendered within the group.",
      table: { type: { summary: "ReactNode" } },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: { type: { summary: "string" } },
    },
  },
  args: {
    direction: "vertical",
    defaultValue: "option1",
  },
} satisfies Meta<typeof RadioGroup>

export default meta

type Story = StoryObj<typeof RadioGroup>

/** Default RadioGroup (demonstrates basic functionality) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview RadioGroup with default settings. Shows a vertical group of radio buttons with one option pre-selected.",
      },
    },
  },
  render: (args) => (
    <RadioGroup {...args} name="overview">
      <Radio value="option1">Option 1</Radio>
      <Radio value="option2">Option 2</Radio>
      <Radio value="option3">Option 3</Radio>
    </RadioGroup>
  ),
}

/** Radio component props demonstration */
export const RadioProps: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstration of individual Radio component props including children, value, disabled state, and labelProps.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
        gap: 24,
        alignItems: "flex-start",
      }}
    >
      <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
        <Typography
          level="bodyLarge"
          style={{ fontWeight: "600", marginBottom: 8 }}
        >
          Basic Radio Props
        </Typography>
        <RadioGroup name="basic-props" defaultValue="text">
          <Radio value="text">Text Label (children)</Radio>
          <Radio value="custom" labelProps={{ style: { color: "#0369a1", fontWeight: "600" } }}>
            Custom Label Styling
          </Radio>
          <Radio value="disabled" disabled>
            Disabled Radio
          </Radio>
        </RadioGroup>
      </div>

      <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
        <Typography
          level="bodyLarge"
          style={{ fontWeight: "600", marginBottom: 8 }}
        >
          Value Types
        </Typography>
        <RadioGroup name="value-types" defaultValue="string-value">
          <Radio value="string-value">String Value</Radio>
          <Radio value={123}>Number Value (123)</Radio>
          <Radio value="special-chars">Special Characters (!@#)</Radio>
        </RadioGroup>
      </div>

      <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
        <Typography
          level="bodyLarge"
          style={{ fontWeight: "600", marginBottom: 8 }}
        >
          Custom Content
        </Typography>
        <RadioGroup name="custom-content" defaultValue="rich">
          <Radio value="rich">
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <span style={{ fontSize: "18px" }}>🎨</span>
              <span>Rich Content</span>
            </div>
          </Radio>
          <Radio value="multiline">
            <div>
              <div style={{ fontWeight: "600" }}>Primary Text</div>
              <div style={{ fontSize: "12px", color: "#666" }}>Secondary description</div>
            </div>
          </Radio>
          <Radio value="badge">
            <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
              <span>With Badge</span>
              <span style={{
                backgroundColor: "#ef4444",
                color: "white",
                padding: "2px 6px",
                borderRadius: "4px",
                fontSize: "10px"
              }}>
                NEW
              </span>
            </div>
          </Radio>
        </RadioGroup>
      </div>
    </div>
  ),
}

/** RadioGroup with different states */
export const States: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of all RadioGroup states including default, checked, and disabled variations.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 20,
          alignItems: "flex-start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Default States
          </Typography>
          <RadioGroup name="default-states" defaultValue="option2">
            <Radio value="option1">Unchecked</Radio>
            <Radio value="option2">Checked</Radio>
            <Radio value="option3">Another Option</Radio>
          </RadioGroup>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Disabled States
          </Typography>
          <RadioGroup name="disabled-states" defaultValue="disabled2">
            <Radio value="disabled1" disabled>Disabled Unchecked</Radio>
            <Radio value="disabled2" disabled>Disabled Checked</Radio>
            <Radio value="disabled3" disabled>Disabled Option</Radio>
          </RadioGroup>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Interactive Examples
          </Typography>
          <RadioGroup name="interactive" defaultValue="small">
            <Radio value="small">Small Size</Radio>
            <Radio value="medium">Medium Size</Radio>
            <Radio value="large">Large Size</Radio>
          </RadioGroup>
        </div>
      </div>
    )
  },
}

/** Controlled RadioGroup example */
export const Controlled: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A controlled RadioGroup that manages its own state with React hooks.",
      },
    },
  },
  render: () => {
    function ControlledDemo() {
      const [selectedValue, setSelectedValue] = useState("medium")

      const handleChange = useCallback((value: string) => {
        setSelectedValue(value)
      }, [])

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 16,
            alignItems: "flex-start",
          }}
        >
          <RadioGroup
            name="controlled"
            value={selectedValue}
            onValueChange={handleChange}
          >
            <Radio value="small">Small</Radio>
            <Radio value="medium">Medium</Radio>
            <Radio value="large">Large</Radio>
          </RadioGroup>
          <Typography level="bodySmall" style={{ color: "#6b7280" }}>
            Selected: {selectedValue}
          </Typography>
        </div>
      )
    }
    return <ControlledDemo />
  },
}

/** RadioGroup with horizontal layout */
export const HorizontalLayout: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "RadioGroup with radio buttons arranged horizontally for compact layouts. ⚠️ Horizontal layout is not recommended.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: 16,
        alignItems: "flex-start",
      }}
    >
      <div>
        <Typography level="bodyLarge" style={{ fontWeight: "600", marginBottom: 8 }}>
          Size Selection
        </Typography>
        <RadioGroup name="size-horizontal" defaultValue="medium" direction="horizontal">
          <Radio value="small">S</Radio>
          <Radio value="medium">M</Radio>
          <Radio value="large">L</Radio>
          <Radio value="xlarge">XL</Radio>
        </RadioGroup>
      </div>

      <div>
        <Typography level="bodyLarge" style={{ fontWeight: "600", marginBottom: 8 }}>
          Rating
        </Typography>
        <RadioGroup name="rating-horizontal" defaultValue="4" direction="horizontal">
          <Radio value="1">1★</Radio>
          <Radio value="2">2★</Radio>
          <Radio value="3">3★</Radio>
          <Radio value="4">4★</Radio>
          <Radio value="5">5★</Radio>
        </RadioGroup>
      </div>
    </div>
  ),
}

/** RadioGroup form integration example */
export const FormIntegration: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Example of RadioGroup components integrated into a real-world form with submit functionality and validation.",
      },
    },
  },
  render: () => {
    function FormDemo() {
      const [formData, setFormData] = useState({
        size: "medium",
        color: "blue",
        shipping: "standard",
      })
      const [isSubmitting, setIsSubmitting] = useState(false)
      const [lastSubmitted, setLastSubmitted] = useState<typeof formData | null>(null)

      const handleSizeChange = useCallback((value: string) => {
        setFormData((prev) => ({ ...prev, size: value }))
      }, [])

      const handleColorChange = useCallback((value: string) => {
        setFormData((prev) => ({ ...prev, color: value }))
      }, [])

      const handleShippingChange = useCallback((value: string) => {
        setFormData((prev) => ({ ...prev, shipping: value }))
      }, [])

      const handleSubmit = useCallback(async (e: React.FormEvent) => {
        e.preventDefault()
        setIsSubmitting(true)

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))

        setLastSubmitted({ ...formData })
        setIsSubmitting(false)
      }, [formData])

      return (
        <form
          onSubmit={handleSubmit}
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 24,
            maxWidth: 400,
          }}
        >
          <div>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 12 }}
            >
              Size
            </Typography>
            <RadioGroup
              name="size"
              value={formData.size}
              onValueChange={handleSizeChange}
            >
              <Radio value="small">Small</Radio>
              <Radio value="medium">Medium</Radio>
              <Radio value="large">Large</Radio>
            </RadioGroup>
          </div>

          <div>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 12 }}
            >
              Color
            </Typography>
            <RadioGroup
              name="color"
              value={formData.color}
              onValueChange={handleColorChange}
            >
              <Radio value="red">Red</Radio>
              <Radio value="blue">Blue</Radio>
              <Radio value="green">Green</Radio>
            </RadioGroup>
          </div>

          <div>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 12 }}
            >
              Shipping
            </Typography>
            <RadioGroup
              name="shipping"
              value={formData.shipping}
              onValueChange={handleShippingChange}
            >
              <Radio value="standard">Standard (5-7 days)</Radio>
              <Radio value="express">Express (2-3 days)</Radio>
              <Radio value="overnight">Overnight</Radio>
            </RadioGroup>
          </div>

          <div style={{ borderTop: "1px solid #e5e7eb", paddingTop: 16 }}>
            <Button
              type="submit"
              disabled={isSubmitting}
              style={{ marginBottom: 12 }}
            >
              {isSubmitting ? "Processing..." : "Add to Cart"}
            </Button>

            <Typography
              level="bodySmall"
              style={{ color: "#6b7280" }}
            >
              Selection: {formData.size} {formData.color} with {formData.shipping} shipping
            </Typography>

            {lastSubmitted && (
              <Typography
                level="bodySmall"
                style={{ color: "#10b981", marginTop: 8 }}
              >
                ✓ Order submitted successfully!
              </Typography>
            )}
          </div>
        </form>
      )
    }
    return <FormDemo />
  },
}

/** RadioGroup with custom styling and advanced usage */
export const AdvancedUsage: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Advanced RadioGroup usage examples showing custom styling, complex layouts, and real-world scenarios.",
      },
    },
  },
  render: () => {
    function AdvancedDemo() {
      const [paymentMethod, setPaymentMethod] = useState("credit")
      const [billingAddress, setBillingAddress] = useState("same")

      const getPaymentMethodLabel = () => {
        if (paymentMethod === "credit") return "Credit Card"
        if (paymentMethod === "paypal") return "PayPal"
        return "Bank Transfer"
      }

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 32,
            maxWidth: 500,
          }}
        >
          {/* Payment Method Section */}
          <div
            style={{
              padding: 20,
              border: "1px solid #e5e7eb",
              borderRadius: 8,
              backgroundColor: "#f9fafb",
            }}
          >
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 16 }}
            >
              Payment Method
            </Typography>
            <RadioGroup
              name="payment-method"
              value={paymentMethod}
              onValueChange={setPaymentMethod}
            >
              <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
                <div style={{ padding: 12, backgroundColor: "white", borderRadius: 6, border: paymentMethod === "credit" ? "2px solid #3b82f6" : "1px solid #e5e7eb" }}>
                  <Radio value="credit">
                    <div>
                      <Typography level="bodyLarge" style={{ fontWeight: "500" }}>
                        Credit Card
                      </Typography>
                      <Typography level="bodySmall" style={{ color: "#6b7280" }}>
                        Visa, Mastercard, American Express
                      </Typography>
                    </div>
                  </Radio>
                </div>
                <div style={{ padding: 12, backgroundColor: "white", borderRadius: 6, border: paymentMethod === "paypal" ? "2px solid #3b82f6" : "1px solid #e5e7eb" }}>
                  <Radio value="paypal">
                    <div>
                      <Typography level="bodyLarge" style={{ fontWeight: "500" }}>
                        PayPal
                      </Typography>
                      <Typography level="bodySmall" style={{ color: "#6b7280" }}>
                        Pay with your PayPal account
                      </Typography>
                    </div>
                  </Radio>
                </div>
                <div style={{ padding: 12, backgroundColor: "white", borderRadius: 6, border: paymentMethod === "bank" ? "2px solid #3b82f6" : "1px solid #e5e7eb" }}>
                  <Radio value="bank">
                    <div>
                      <Typography level="bodyLarge" style={{ fontWeight: "500" }}>
                        Bank Transfer
                      </Typography>
                      <Typography level="bodySmall" style={{ color: "#6b7280" }}>
                        Direct bank transfer (3-5 business days)
                      </Typography>
                    </div>
                  </Radio>
                </div>
              </div>
            </RadioGroup>
          </div>

          {/* Billing Address Section */}
          <div
            style={{
              padding: 20,
              border: "1px solid #e5e7eb",
              borderRadius: 8,
            }}
          >
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 16 }}
            >
              Billing Address
            </Typography>
            <RadioGroup
              name="billing-address"
              value={billingAddress}
              onValueChange={setBillingAddress}
            >
              <Radio value="same">Same as shipping address</Radio>
              <Radio value="different">Use a different billing address</Radio>
            </RadioGroup>
          </div>

          {/* Summary */}
          <div
            style={{
              padding: 16,
              backgroundColor: "#f0f9ff",
              borderRadius: 8,
              border: "1px solid #0ea5e9",
            }}
          >
            <Typography level="bodySmall" style={{ color: "#0369a1" }}>
              <strong>Summary:</strong> Payment via {getPaymentMethodLabel()},
              billing address {billingAddress === "same" ? "same as shipping" : "different from shipping"}
            </Typography>
          </div>
        </div>
      )
    }
    return <AdvancedDemo />
  },
}
